// pages/shopping/shopping.js
import { loginRequest } from '../../service/index';

Page({
  data: {
    loading: true,
    categories: [],
    currentCategory: '0',
    dishes: [],
    currentCategoryDishes: [],
    cartItems: [],
    cartTotal: {
      count: 0,
      price: 0
    },
    cartVisible: false,
    showDishModal: false,
    selectedDish: {},
    pageNo: 1,
    pageSize: 20,
    hasMore: true,
    showCouponList: false,
    selectedCoupons: [],
    selectedCouponIds: [],
    products: [],
    showCheckout: false,
    couponDiscount: 0,
    finalAmount: 0,
    orderSuccess: false,
    orderId: '',
    // 支付相关数据
    showPaymentOptions: false,
    selectedPaymentMethod: 'wxpay',
    canUseBalance: false,
    userBalance: 0,
    payableAmount: 0,
    paymentOrderData: null,
    enterpriseList: [], // 企业列表
    selectedEnterprise: null, // 选中的企业ID
    source: 'shopping'
  },

  onLoad: function (options) {
    wx.setNavigationBarTitle({
      title: '素食好物'
    });
    
    // 获取企业列表
    this.getReserveConfig();
    
    this.fetchCategories();
  },

  /**
   * 获取预约配置和企业列表
   */
  getReserveConfig() {
    wx.request({
      url: `${getApp().globalData.baseUrl}/reserve/config`,
      method: 'GET',
      header: {
        'token': wx.getStorageSync('token')
      },
      data: {
        type: 'shopping',
        source: this.data.source || 'shopping'
      },
      success: (res) => {
        if (res.data.code === 200) {
          // 静默保存企业列表
          this.setData({
            enterpriseList: res.data.data.enterprise_list || []
          });
        }
      },
      fail: (err) => {
        console.error('获取配置失败', err);
      }
    });
  },

  onPullDownRefresh: function () {
    this.setData({
      pageNo: 1,
      hasMore: true
    });
    this.fetchDishes(this.data.currentCategory);
    wx.stopPullDownRefresh();
  },

  onReachBottom: function () {
    if (this.data.hasMore && !this.data.loading) {
      this.setData({
        pageNo: this.data.pageNo + 1
      });
      this.fetchDishes(this.data.currentCategory, true);
    }
  },

  /**
   * 获取商品分类
   */
  fetchCategories() {
    this.setData({ loading: true });

    loginRequest.get({
      url: '/menu/categories/search',
      data: {
        key: 'product',
        show_root: false
      },
      header: {
        'token': wx.getStorageSync('token')
      }
    }).then(res => {
      if (res.status === 200 && res.data && res.data.length > 0) {
        const categories = res.data || [];
        this.setData({
          categories,
          currentCategory: categories[0]?.id || '0',
          loading: false
        });
        
        // 获取第一个分类的菜品
        this.fetchDishes(categories[0]?.id || '0');
      } else {
        this.setData({
          categories: [],
          loading: false
        });
        // 如果没有分类，仍然尝试获取菜品
        this.fetchDishes('0');
      }
    }).catch(err => {
      console.error('获取分类失败', err);
      this.setData({
        categories: [],
        loading: false
      });
      this.fetchDishes('0');
    });
  },

  /**
   * 获取菜品列表
   */
  fetchDishes(categoryId, append = false) {
    const { pageNo, pageSize } = this.data;

    this.setData({
      loading: !append
    });

    // 从全局配置中获取商店ID
    const app = getApp();
    const storeId = app.globalData.defaultStoreId || 1;

    loginRequest.get({
      url: '/menu/dishes',
      data: {
        store_id: storeId,
        category_id: categoryId,
        page_no: pageNo,
        page_size: pageSize
      },
      header: {
        'token': wx.getStorageSync('token')
      }
    }).then(res => {
      if (res.status === 200) {
        const newDishes = res.data.list || [];
        const hasMore = newDishes.length === pageSize;

        // 处理菜品数据，添加购物车中的数量
        newDishes.forEach(dish => {
          const cartItem = this.data.cartItems.find(item => item.id === dish.id);
          dish.count = cartItem ? cartItem.count : 0;
        });

        if (append) {
          // 加载更多
          const updatedDishes = [...this.data.currentCategoryDishes, ...newDishes];
          this.setData({
            currentCategoryDishes: updatedDishes,
            hasMore,
            loading: false
          });
        } else {
          // 重新加载
          this.setData({
            dishes: newDishes,
            currentCategoryDishes: newDishes,
            pageNo: 2,
            hasMore,
            loading: false
          });
        }
      } else {
        this.setData({
          loading: false
        });
        if (!append) {
          this.setData({
            currentCategoryDishes: []
          });
        }

        // 显示错误提示
        if (res.message) {
          wx.showToast({
            title: res.message,
            icon: 'none'
          });
        }
      }
    }).catch(err => {
      console.error('获取菜品失败', err);
      this.setData({
        loading: false
      });
      if (!append) {
        this.setData({
          currentCategoryDishes: []
        });
      }

      // 显示网络错误提示
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      });
    });
  },

  /**
   * 选择分类
   */
  selectCategory(e) {
    const categoryId = e.currentTarget.dataset.id;
    this.setData({
      currentCategory: categoryId,
      pageNo: 1,
      hasMore: true
    });
    this.fetchDishes(categoryId);
  },

  /**
   * 添加菜品到购物车
   */
  addDish(e) {
    const dishId = e.currentTarget.dataset.id;
    this.updateCart(dishId, 1);
  },

  /**
   * 从购物车减少菜品
   */
  minusDish(e) {
    const dishId = e.currentTarget.dataset.id;
    this.updateCart(dishId, -1);
  },

  /**
   * 在弹窗中添加菜品
   */
  addDishInModal(e) {
    const dishId = e.currentTarget.dataset.id;
    this.updateCart(dishId, 1, true);
  },

  /**
   * 在弹窗中减少菜品
   */
  minusDishInModal(e) {
    const dishId = e.currentTarget.dataset.id;
    this.updateCart(dishId, -1, true);
  },

  /**
   * 更新购物车
   */
  updateCart(dishId, change, isModal = false) {
    // 查找菜品
    const dish = this.data.dishes.find(item => item.id === dishId) || 
                 this.data.currentCategoryDishes.find(item => item.id === dishId);
    if (!dish) return;

    // 更新菜品计数
    const oldCount = dish.count || 0;
    const newCount = Math.max(0, oldCount + change);
    dish.count = newCount;

    // 更新所有菜品列表中的计数
    const dishes = this.data.dishes.map(item => {
      if (item.id === dishId) {
        item.count = newCount;
      }
      return item;
    });

    const currentCategoryDishes = this.data.currentCategoryDishes.map(item => {
      if (item.id === dishId) {
        item.count = newCount;
      }
      return item;
    });

    // 更新购物车
    let cartItems = [...this.data.cartItems];

    if (newCount > 0) {
      const cartItemIndex = cartItems.findIndex(item => item.id === dishId);
      if (cartItemIndex >= 0) {
        cartItems[cartItemIndex].count = newCount;
      } else {
        cartItems.push({
          id: dish.id,
          name: dish.name,
          price: dish.price,
          count: newCount
        });
      }
    } else {
      cartItems = cartItems.filter(item => item.id !== dishId);
    }

    // 计算购物车总数和总价
    const cartTotal = this.calculateCartTotal(cartItems);

    // 更新选中的菜品（如果在弹窗中）
    const updateData = {
      dishes,
      currentCategoryDishes,
      cartItems,
      cartTotal
    };

    // 如果是在弹窗中操作，同时更新选中的菜品
    if (isModal) {
      updateData.selectedDish = { ...dish };
    }

    this.setData(updateData);
  },

  /**
   * 计算购物车总数和总价
   */
  calculateCartTotal(cartItems) {
    let count = 0;
    let price = 0;

    cartItems.forEach(item => {
      count += item.count;
      price += item.price * item.count;
    });

    return {
      count,
      price: parseFloat(price.toFixed(2))
    };
  },

  /**
   * 切换购物车展开/收起状态
   */
  toggleCart() {
    if (this.data.cartTotal.count > 0) {
      this.setData({
        cartVisible: !this.data.cartVisible
      });
    }
  },

  /**
   * 清空购物车
   */
  clearCart() {
    wx.showModal({
      title: '提示',
      content: '确定要清空购物车吗？',
      success: (res) => {
        if (res.confirm) {
          // 重置所有菜品的count
          const dishes = this.data.dishes.map(item => ({ ...item, count: 0 }));
          const currentCategoryDishes = this.data.currentCategoryDishes.map(item => ({ ...item, count: 0 }));

          this.setData({
            dishes,
            currentCategoryDishes,
            cartItems: [],
            cartTotal: { count: 0, price: 0 },
            cartVisible: false
          });
        }
      }
    });
  },

  /**
   * 订单成功后清空购物车和优惠券
   */
  clearCartAfterOrder() {
    // 重置所有菜品的count
    const dishes = this.data.dishes.map(item => ({ ...item, count: 0 }));
    const currentCategoryDishes = this.data.currentCategoryDishes.map(item => ({ ...item, count: 0 }));

    this.setData({
      dishes,
      currentCategoryDishes,
      cartItems: [],
      cartTotal: { count: 0, price: 0 },
      cartVisible: false,
      // 清空优惠券相关数据
      selectedCoupons: [],
      selectedCouponIds: [],
      couponDiscount: 0,
      finalAmount: 0
    });
  },

  /**
   * 显示菜品详情
   */
  showDishDetail(e) {
    const dish = e.currentTarget.dataset.dish;
    this.setData({
      selectedDish: dish,
      showDishModal: true
    });
  },

  /**
   * 隐藏菜品详情
   */
  hideDishDetail() {
    this.setData({
      showDishModal: false,
      selectedDish: {}
    });
  },

  /**
   * 去结算
   */
  goToCheckout() {
    if (this.data.cartTotal.count === 0) {
      wx.showToast({
        title: '请先选择商品',
        icon: 'none'
      });
      return;
    }

    this.setData({
      showCheckout: true,
      finalAmount: this.data.cartTotal.price
    });
  },

  /**
   * 隐藏结算页面
   */
  hideCheckout() {
    this.setData({
      showCheckout: false
    });
  },

  /**
   * 创建购物订单
   */
  createShoppingOrder() {
    wx.showLoading({
      title: '提交中...'
    });

    // 准备订单数据
    const orderItems = this.data.cartItems.map(item => ({
      dish_id: item.id,
      name: item.name,
      price: item.price,
      quantity: item.count
    }));

    // 准备优惠券ID字符串
    let couponIdString = '';
    if (this.data.selectedCoupons && this.data.selectedCoupons.length > 0) {
      couponIdString = this.data.selectedCoupons
        .map(coupon => coupon.coupon_usage_record_id)
        .join(',');
    }

    const orderData = {
      order_type: 'shopping',
      items: orderItems,
      total_amount: this.data.finalAmount || this.data.cartTotal.price,
      coupon_id: couponIdString,
      coupon_discount: this.data.couponDiscount || 0
    };

    console.log('提交订单数据:', orderData);

    loginRequest.post({
      url: '/shopping-order/create',
      data: orderData,
      header: {
        'token': wx.getStorageSync('token')
      }
    }).then(res => {
      // 确保无论如何都隐藏加载提示
      wx.hideLoading();

      // 添加调试日志，查看完整的响应数据
      console.log('订单创建响应:', JSON.stringify(res));

      // 检查返回的数据格式
      if (res.order_no) {  // 直接检查返回的数据是否包含订单号
        // 显示支付选择弹窗
        this.showPaymentOptions(res);
      } else if (res.status === 401) {
        // 未登录或token过期，跳转到授权页面
        wx.navigateTo({
          url: '/pages/phoneAuth/phoneAuth?redirect=shopping'
        });
      } else {
        // 处理其他错误情况
        wx.showToast({
          title: res.message || '提交失败',
          icon: 'none',
          duration: 2000
        });
      }
    }).catch(err => {
      console.error('请求失败', err);
      wx.hideLoading();

      wx.showToast({
        title: err.message || '网络错误，请重试',
        icon: 'none',
        duration: 2000
      });
    });
  },

  /**
   * 生成产品列表
   */
  generateProductList() {
    return this.data.cartItems.map(item => ({
      product_id: item.id,
      quantity: item.count
    }));
  },

  /**
   * 显示优惠券列表
   */
  showCouponList() {
    this.setData({
      showCouponList: true,
      products: this.generateProductList()
    });
  },

  /**
   * 隐藏优惠券列表
   */
  hideCouponList() {
    this.setData({
      showCouponList: false
    });
  },

  /**
   * 选择优惠券
   */
  onCouponSelect(e) {
    const { couponIds, coupons } = e.detail;

    if (coupons && coupons.length > 0) {
      // 调用后端API计算准确的优惠金额
      this.calculateCouponPricing(couponIds, coupons);
    } else {
      // 取消选择所有优惠券
      this.setData({
        selectedCoupons: [],
        selectedCouponIds: [],
        couponDiscount: 0,
        finalAmount: this.data.cartTotal.price,
        showCouponList: false
      });

      wx.showToast({
        title: '已取消选择',
        icon: 'none'
      });
    }
  },

  /**
   * 计算优惠券价格
   */
  calculateCouponPricing(couponIds, coupons) {
    const app = getApp();
    const userInfo = app.globalData.userInfo;

    if (!userInfo || !userInfo.id) {
      wx.showToast({
        title: '用户信息获取失败',
        icon: 'none'
      });
      return;
    }

    const products = this.generateProductList();

    if (!couponIds || couponIds.length === 0) {
      this.setData({
        selectedCoupons: [],
        selectedCouponIds: [],
        couponDiscount: 0,
        finalAmount: this.data.cartTotal.price
      });
      return;
    }

    const requestData = {
      user_id: userInfo.id,
      products: products,
      coupon_usage_record_ids: couponIds
    };

    wx.showLoading({
      title: '计算优惠中...'
    });

    wx.request({
      url: `${app.globalData.baseUrl}/coupon/coupon-pricing`,
      method: 'POST',
      header: {
        'Content-Type': 'application/json',
        'token': wx.getStorageSync('token')
      },
      data: requestData,
      success: (res) => {
        wx.hideLoading();

        if (res.data && res.data.status === 200) {
          const data = res.data.data;
          const pricingResult = data.pricing_result;

          if (pricingResult && pricingResult.discount) {
            const totalDiscount = pricingResult.discount.total_discount || 0;
            const discountCoupons = pricingResult.discount.coupons || [];

            const couponsWithDiscount = coupons.map(coupon => {
              const discountInfo = discountCoupons.find(dc =>
                dc.coupon_usage_record_id === coupon.coupon_usage_record_id
              );

              return {
                ...coupon,
                discountAmount: discountInfo ? discountInfo.discount_amount : 0
              };
            });

            const finalAmount = Math.max(0, this.data.cartTotal.price - totalDiscount);

            this.setData({
              selectedCoupons: couponsWithDiscount,
              selectedCouponIds: couponIds,
              couponDiscount: totalDiscount,
              finalAmount: finalAmount,
              showCouponList: false
            });

            wx.showToast({
              title: `已选择${coupons.length}张优惠券，优惠¥${totalDiscount}`,
              icon: 'success'
            });
          } else {
            wx.showToast({
              title: '计算优惠失败',
              icon: 'none'
            });
          }
        } else {
          wx.showToast({
            title: res.data?.message || '计算优惠失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('计算优惠券价格失败', err);
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 取消单张优惠券
   */
  cancelCoupon(e) {
    const couponId = e.currentTarget.dataset.couponId;

    if (!couponId) {
      return;
    }

    const updatedCoupons = this.data.selectedCoupons.filter(coupon =>
      coupon.coupon_usage_record_id !== couponId
    );

    const updatedCouponIds = this.data.selectedCouponIds.filter(id =>
      id !== couponId.toString()
    );

    if (updatedCoupons.length === 0) {
      this.setData({
        selectedCoupons: [],
        selectedCouponIds: [],
        couponDiscount: 0,
        finalAmount: this.data.cartTotal.price
      });

      wx.showToast({
        title: '已取消所有优惠券',
        icon: 'none'
      });
    } else {
      this.calculateCouponPricing(updatedCouponIds, updatedCoupons);

      wx.showToast({
        title: '已取消该优惠券',
        icon: 'none'
      });
    }
  },

  /**
   * 清空所有优惠券
   */
  clearAllCoupons() {
    wx.showModal({
      title: '确认清空',
      content: '确定要清空所有已选择的优惠券吗？',
      success: (res) => {
        if (res.confirm) {
          this.setData({
            selectedCoupons: [],
            selectedCouponIds: [],
            couponDiscount: 0,
            finalAmount: this.data.cartTotal.price
          });

          wx.showToast({
            title: '已清空所有优惠券',
            icon: 'success'
          });
        }
      }
    });
  },

  /**
   * 提交订单
   */
  submitOrder() {
    // 检查登录状态
    const token = wx.getStorageSync('token');
    if (!token) {
      wx.navigateTo({
        url: '/pages/phoneAuth/phoneAuth?redirect=shopping'
      });
      return;
    }

    this.createShoppingOrder();
  },

  /**
   * 跳转到订单详情
   */
  goToOrderDetail() {
    wx.switchTab({
      url: '/pages/reserve/reserve?tab=all'
    });
  },

  /**
   * 显示支付选择弹窗
   */
  showPaymentOptions(orderData) {
    // 使用最终金额（扣除优惠券后的金额）
    const finalPayableAmount = this.data.finalAmount || orderData.payable_amount;
    
    // 判断用户余额是否足够支付最终金额
    const canUseBalance = orderData.user_balance >= finalPayableAmount;

    this.setData({
      showPaymentOptions: true,
      paymentOrderData: orderData,
      selectedPaymentMethod: 'wxpay', // 默认选择微信支付
      canUseBalance: canUseBalance,
      userBalance: orderData.user_balance,
      payableAmount: finalPayableAmount, // 使用扣除优惠券后的最终金额
      // 如果有企业列表，默认选择第一个企业
      selectedEnterprise: this.data.enterpriseList.length > 0 ? this.data.enterpriseList[0].id : null
    });
  },

  /**
   * 选择支付方式
   */
  selectPaymentMethod(e) {
    const method = e.currentTarget.dataset.method;
    this.setData({
      selectedPaymentMethod: method,
      // 如果切换到企业支付，默认选择第一个企业
      selectedEnterprise: method === 'biz_enterprise' && this.data.enterpriseList.length > 0
        ? this.data.enterpriseList[0].id
        : this.data.selectedEnterprise
    });
  },

  /**
   * 选择企业
   */
  selectEnterprise(e) {
    const id = e.currentTarget.dataset.id;
    console.log('选择企业:', id);
    this.setData({
      selectedEnterprise: id
    }, () => {
      console.log('更新后的selectedEnterprise:', this.data.selectedEnterprise);
    });
  },

  /**
   * 确认支付
   */
  confirmPayment() {
    const { selectedPaymentMethod, paymentOrderData, selectedEnterprise } = this.data;

    // 如果是企业支付，检查是否选择了企业
    if (selectedPaymentMethod === 'biz_enterprise' && !selectedEnterprise) {
      wx.showToast({
        title: '请选择企业',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({
      title: '处理中...',
    });

    wx.request({
      url: `${getApp().globalData.baseUrl}/order/pay/create`,
      method: 'POST',
      header: {
        'token': wx.getStorageSync('token')
      },
      data: {
        order_no: paymentOrderData.order_no,
        paymentMethod: selectedPaymentMethod,
        amount: this.data.payableAmount, // 使用页面中设置的金额（已扣除优惠券）
        enterprise_id: selectedPaymentMethod === 'biz_enterprise' ? selectedEnterprise : undefined
      },
      success: (res) => {
        wx.hideLoading();
        if (res.data.status === 200) {
          if (selectedPaymentMethod === 'wxpay') {
            const payParams = res.data.payParams;

            // 显示支付提示
            wx.showLoading({
              title: '正在调起支付...',
              mask: true
            });

            // 调起微信支付
            wx.requestPayment({
              ...payParams,
              success: () => {
                wx.hideLoading();
                this.paymentSuccess();
              },
              fail: (err) => {
                wx.hideLoading();
                console.error('支付失败', err);

                // 区分用户取消和真正的支付错误
                if (err.errMsg && err.errMsg.includes('cancel')) {
                  wx.showToast({
                    title: '支付已取消',
                    icon: 'none',
                    duration: 1500
                  });
                } else {
                  wx.showToast({
                    title: '支付失败，请重试',
                    icon: 'none',
                    duration: 2000
                  });
                }
              },
              complete: () => {
                // 确保loading被隐藏
                wx.hideLoading();
              }
            });
          } else if (selectedPaymentMethod === 'balance') {
            this.balancePaymentSuccess();
          } else if (selectedPaymentMethod === 'biz_enterprise') {
            this.paymentSuccess();
          }
        } else {
          wx.showToast({
            title: res.data.message || '支付失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('请求失败', err);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 余额支付成功
   */
  balancePaymentSuccess() {
    // 先更新页面状态
    this.setData({
      showPaymentOptions: false,
      orderSuccess: true,
      orderId: this.data.paymentOrderData.order_no
    });

    // 清空购物车和优惠券
    this.clearCartAfterOrder();

    // 显示成功提示
    wx.showToast({
      title: '余额支付成功',
      icon: 'success',
      duration: 2000
    });

    // 刷新用户信息
    this.refreshUserInfo();
  },

  /**
   * 微信支付成功处理
   */
  paymentSuccess() {
    // 先更新页面状态
    this.setData({
      showPaymentOptions: false,
      orderSuccess: true,
      orderId: this.data.paymentOrderData.order_no
    });

    // 清空购物车和优惠券
    this.clearCartAfterOrder();

    // 显示成功提示
    wx.showToast({
      title: '支付成功',
      icon: 'success',
      duration: 2000
    });

    // 刷新用户信息
    this.refreshUserInfo();
  },

  /**
   * 刷新用户信息
   */
  refreshUserInfo() {
    const app = getApp();
    // 这里可以添加刷新用户信息的逻辑
    // 由于shopping页面不需要像booking_business那样复杂的用户信息处理
    // 简化处理即可
  },

  /**
   * 关闭支付弹窗
   */
  closePayment() {
    this.setData({
      showPaymentOptions: false
    });
  }
});
