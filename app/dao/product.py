from operator import and_
from typing import List, Optional, Dict
from sqlalchemy import or_, func

from sqlalchemy.orm import Session

from app.dao.base import DAO
from app.models.content import product_content_relation, Content
from app.models.enum import Status
from app.models.pricing import PricingStrategy, product_pricing_strategy_relation
from app.models.product import (
    Product, DirectSaleProduct, ReservationProduct
)
from app.models.product import ProductType
from app.models.rule import Rule, product_rule_relation, RuleType, DiningReservationRule
from app.models.tag import Tag
from app.schemas.product import (
    ProductCreate, ProductUpdate,
    DirectSaleProductCreate, DirectSaleProductUpdate,
    ReservationProductCreate, ReservationProductUpdate
)
from app.models.product import MealType
from app.models.product import ProductCategory
from app.schemas.product import ProductCategoryCreate, ProductCategoryUpdate


class ProductDAO(DAO):
    """产品 DAO 类"""

    def __init__(self):
        super().__init__(Product)

    def create(self, session: Session, product: ProductCreate) -> Product:
        """创建产品"""
        product_data = product.model_dump()
        return super().create(session, **product_data)

    def get(self, session: Session, product_id: int) -> Optional[Product]:
        """获取产品"""
        return super().get(session, product_id)

    def get_list(self, session: Session, skip: int = 0, limit: int = 100) -> List[Product]:
        """获取产品列表"""
        return session.query(self.model).offset(skip).limit(limit).all()

    def get_active_products(self, session: Session, skip: int = 0, limit: int = 100) -> List[Product]:
        """获取活跃产品列表"""
        return session.query(self.model).filter(
            self.model.status == Status.ACTIVE
        ).offset(skip).limit(limit).all()

    def search(self, session: Session, keyword: str = None, name: str = None, product_type: ProductType = None,
               status=None, skip: int = 0, limit: int = 100) -> dict:
        """
        根据条件搜索产品列表及总数
        
        Args:
            session: 数据库会话
            keyword: 搜索关键字，可搜索产品名称、产品描述等
            name: 产品名称（模糊匹配）
            product_type: 产品类型
            status: 产品状态
            skip: 分页起始位置
            limit: 分页大小
            
        Returns:
            dict: 包含 'total'（搜索结果总数）和 'list'（分页后的产品列表）的字典
        """
        query = session.query(self.model)

        # 如果提供了关键字，则添加过滤条件
        if keyword:
            search_keyword = f"%{keyword}%"
            query = query.filter(
                or_(
                    self.model.name.like(search_keyword),
                    self.model.description.like(search_keyword)
                )
            )

        # 按产品名称过滤
        if name:
            search_name = f"%{name}%"
            query = query.filter(self.model.name.like(search_name))

        # 按产品类型过滤
        if product_type is not None:
            query = query.filter(self.model.type == product_type)

        # 按状态过滤
        if status is not None:
            query = query.filter(self.model.status == status)

        # 查询总数
        total = query.count()
        # 查询分页后的产品列表
        items = query.order_by(self.model.id.desc()).offset(skip).limit(limit).all()

        return {
            "total": total,
            "list": items
        }

    def update(self, session: Session, product_id: int, product: ProductUpdate) -> Optional[Product]:
        """更新产品"""
        product_data = product.model_dump(exclude_unset=True)
        if 'type' in product_data:
            # 不允许修改type
            del product_data['type']
        return super().update(session, product_id, **product_data)

    def delete(self, session: Session, product_id: int) -> bool:
        """删除产品"""
        return super().delete(session, product_id)

    def get_by_type(self, session: Session, product_type: ProductType, skip: int = 0, limit: int = 100) -> List[
        Product]:
        """根据产品类型获取产品列表"""
        return session.query(self.model).filter(
            self.model.type == product_type
        ).offset(skip).limit(limit).all()

    def get_by_tags(self, session: Session, tag_ids: List[int], skip: int = 0, limit: int = 100) -> List[Product]:
        """根据标签获取产品列表"""
        return session.query(self.model).filter(
            self.model.tags.any(Tag.id.in_(tag_ids))
        ).offset(skip).limit(limit).all()

    def get_pricing_strategies_by_product(self, session: Session, product_id: int, skip: int = 0, limit: int = 100) -> Dict:
        """获取与特定产品绑定的所有定价策略

        Args:
            session: 数据库会话
            product_id: 产品ID
            skip: 跳过数量
            limit: 限制数量

        Returns:
            Dict: 包含总数和定价策略列表的字典
        """
        product = session.query(Product).filter(Product.id == product_id).first()
        if not product:
            return {"total": 0, "list": []}
            
        total = len(product.pricing_strategies)
        strategies = product.pricing_strategies[skip:skip + limit]
        
        return {
            "total": total,
            "list": strategies
        }

    def get_rules_by_product(self, session: Session, product_id: int, skip: int = 0, limit: int = 100) -> Dict:
        """获取与特定产品绑定的所有规则

        Args:
            session: 数据库会话
            product_id: 产品ID
            skip: 跳过数量
            limit: 限制数量

        Returns:
            Dict: 包含总数和规则列表的字典
        """
        product = session.query(Product).filter(Product.id == product_id).first()
        if not product:
            return {"total": 0, "list": []}
            
        total = len(product.rules)
        rules = product.rules[skip:skip + limit]
        
        return {
            "total": total,
            "list": rules
        }

    def get_product_contents_by_search(self, session: Session, product_id: int, skip: int = 0, limit: int = 100) -> dict:
        """获取与特定产品绑定的所有内容

        Args:
            session: 数据库会话
            product_id: 产品ID
            skip: 跳过数量
            limit: 限制数量

        Returns:
            dict: 包含总数和内容列表的字典
        """
        product = session.query(Product).filter(Product.id == product_id).first()
        if not product:
            return {"total": 0, "list": []}
        
        total = len(product.contents)
        contents = product.contents[skip:skip + limit]
        
        return {
            "total": total,
            "list": contents
        }

    def add_contents(self, session: Session, content_ids: List[int], product_id: int) -> dict:
        """批量将内容绑定到产品

        Returns:
            dict: 包含成功和失败的内容ID列表
        """
        product = session.query(Product).filter(Product.id == product_id).first()
        if not product:
            return {"success": [], "failed": content_ids}
        success_ids = []
        failed_ids = []

        for content_id in content_ids:
            # 正确获取Content实例
            content = session.query(Content).filter(Content.id == content_id).first()
            if not content:
                failed_ids.append(content_id)
                continue

            # 检查是否已经绑定
            existing = session.query(product_content_relation).filter(
                and_(
                    product_content_relation.c.content_id == content_id,
                    product_content_relation.c.product_id == product_id
                )
            ).first()

            if not existing:
                product.contents.append(content)
                success_ids.append(content_id)
            else:
                # 已经绑定过，也视为成功
                success_ids.append(content_id)

        session.commit()
        return {"success": success_ids, "failed": failed_ids}

    def remove_contents(self, session: Session, content_ids: List[int], product_id: int) -> dict:
        """批量将内容从产品解绑

        Returns:
            dict: 包含成功和失败的内容ID列表
        """
        product = session.query(Product).filter(Product.id == product_id).first()
        if not product:
            return {"success": [], "failed": content_ids}

        success_ids = []
        failed_ids = []

        for content_id in content_ids:
            # 正确获取Content实例
            content = session.query(Content).filter(Content.id == content_id).first()
            if not content:
                failed_ids.append(content_id)
                continue

            # 检查是否已经绑定
            existing = session.query(product_content_relation).filter(
                and_(
                    product_content_relation.c.content_id == content_id,
                    product_content_relation.c.product_id == product_id
                )
            ).first()

            if existing:
                # 移除关联关系
                product.contents.remove(content)
                success_ids.append(content_id)
            else:
                # 未绑定，也视为成功
                success_ids.append(content_id)

        session.commit()
        return {"success": success_ids, "failed": failed_ids}

    def add_pricing_strategies(self, session: Session, pricing_strategy_ids: List[int], product_id: int) -> dict:
        """批量将定价策略绑定到产品

        Returns:
            dict: 包含成功和失败的定价策略ID列表
        """
        product = session.query(Product).filter(Product.id == product_id).first()
        if not product:
            return {"success": [], "failed": pricing_strategy_ids}
        success_ids = []
        failed_ids = []

        for strategy_id in pricing_strategy_ids:
            # 获取PricingStrategy实例
            strategy = session.query(PricingStrategy).filter(PricingStrategy.id == strategy_id).first()
            if not strategy:
                failed_ids.append(strategy_id)
                continue

            # 检查是否已经绑定
            existing = session.query(product_pricing_strategy_relation).filter(
                and_(
                    product_pricing_strategy_relation.c.pricing_strategy_id == strategy_id,
                    product_pricing_strategy_relation.c.product_id == product_id
                )
            ).first()

            if not existing:
                product.pricing_strategies.append(strategy)
                success_ids.append(strategy_id)
            else:
                # 已经绑定过，也视为成功
                success_ids.append(strategy_id)

        session.commit()
        return {"success": success_ids, "failed": failed_ids}

    def remove_pricing_strategies(self, session: Session, pricing_strategy_ids: List[int], product_id: int) -> dict:
        """批量将定价策略从产品解绑

        Returns:
            dict: 包含成功和失败的定价策略ID列表
        """
        product = session.query(Product).filter(Product.id == product_id).first()
        if not product:
            return {"success": [], "failed": pricing_strategy_ids}

        success_ids = []
        failed_ids = []

        for strategy_id in pricing_strategy_ids:
            # 获取PricingStrategy实例
            strategy = session.query(PricingStrategy).filter(PricingStrategy.id == strategy_id).first()
            if not strategy:
                failed_ids.append(strategy_id)
                continue

            # 检查是否已经绑定
            existing = session.query(product_pricing_strategy_relation).filter(
                and_(
                    product_pricing_strategy_relation.c.pricing_strategy_id == strategy_id,
                    product_pricing_strategy_relation.c.product_id == product_id
                )
            ).first()

            if existing:
                # 移除关联关系
                product.pricing_strategies.remove(strategy)
                success_ids.append(strategy_id)
            else:
                # 未绑定，也视为成功
                success_ids.append(strategy_id)

        session.commit()
        return {"success": success_ids, "failed": failed_ids}

    def add_rules(self, session: Session, rule_ids: List[int], product_id: int) -> dict:
        """批量将规则绑定到产品

        Returns:
            dict: 包含成功和失败的规则ID列表
        """
        product = session.query(Product).filter(Product.id == product_id).first()
        if not product:
            return {"success": [], "failed": rule_ids}
        success_ids = []
        failed_ids = []

        for rule_id in rule_ids:
            # 获取Rule实例
            rule = session.query(Rule).filter(Rule.id == rule_id).first()
            if not rule:
                failed_ids.append(rule_id)
                continue

            # 检查是否已经绑定
            existing = session.query(product_rule_relation).filter(
                and_(
                    product_rule_relation.c.rule_id == rule_id,
                    product_rule_relation.c.product_id == product_id
                )
            ).first()

            if not existing:
                product.rules.append(rule)
                success_ids.append(rule_id)
            else:
                # 已经绑定过，也视为成功
                success_ids.append(rule_id)

        session.commit()
        return {"success": success_ids, "failed": failed_ids}

    def remove_rules(self, session: Session, rule_ids: List[int], product_id: int) -> dict:
        """批量将规则从产品解绑

        Returns:
            dict: 包含成功和失败的规则ID列表
        """
        product = session.query(Product).filter(Product.id == product_id).first()
        if not product:
            return {"success": [], "failed": rule_ids}

        success_ids = []
        failed_ids = []

        for rule_id in rule_ids:
            # 获取Rule实例
            rule = session.query(Rule).filter(Rule.id == rule_id).first()
            if not rule:
                failed_ids.append(rule_id)
                continue

            # 检查是否已经绑定
            existing = session.query(product_rule_relation).filter(
                and_(
                    product_rule_relation.c.rule_id == rule_id,
                    product_rule_relation.c.product_id == product_id
                )
            ).first()

            if existing:
                # 移除关联关系
                product.rules.remove(rule)
                success_ids.append(rule_id)
            else:
                # 未绑定，也视为成功
                success_ids.append(rule_id)

        session.commit()
        return {"success": success_ids, "failed": failed_ids}

    def get_contents_by_product(self, session: Session, product_id: int) -> List[Content]:
        """获取与特定产品绑定的所有内容"""
        product = session.query(Product).filter(Product.id == product_id).first()
        if not product:
            return []

        # 获取关联关系
        relations = session.query(product_content_relation).filter(
            product_content_relation.c.product_id == product_id
        ).all()

        # 如果没有关联内容，直接返回空列表
        if not relations:
            return []

        # 获取内容ID列表
        content_ids = [relation.content_id for relation in relations]

        try:
            # 直接使用指定的列名查询Content表，避免使用可能不存在的status列
            contents = session.query(Content).filter(
                Content.id.in_(content_ids)
            ).all()
            return contents
        except Exception as e:
            # 记录错误并返回空列表
            print(f"查询内容时出错: {str(e)}")
            return []

    def get_products_by_meal_type(self, session: Session, meal_type: MealType) -> List[Product]:
        """根据餐食类型获取产品列表"""
        return session.query(self.model).filter(
            self.model.meal_type == meal_type,
            self.model.status == Status.ACTIVE
        ).all()

    def get_product_with_dining_rules(self, session: Session, product_name: str) -> Dict:
        """
        根据产品名称关键词获取匹配的产品及其关联的dining_reservation类型规则
        
        Args:
            session: 数据库会话
            product_name: 产品名称关键词
            
        Returns:
            Dict: 包含匹配的产品列表和总数的字典
        """
        # 构建模糊匹配查询条件
        search_name = f"%{product_name}%"
        
        # 查询匹配的产品
        products = session.query(self.model).filter(
            self.model.name.like(search_name)
        ).all()
        
        if not products:
            return {"total": 0, "products": []}
            
        result_products = []
        for product in products:
            # 查询该产品关联的所有规则
            product_rules = []
            
            # 获取所有规则ID
            rule_ids = [rule.id for rule in product.rules if rule.type == RuleType.DINING_RESERVATION]
            
            # 一次性查询所有餐厅预订规则
            if rule_ids:
                dining_rule_objs = session.query(DiningReservationRule).filter(
                    DiningReservationRule.id.in_(rule_ids)
                ).all()
                
                # 将规则转换为字典格式
                for rule in dining_rule_objs:
                    product_rules.append({
                        "rule_id": rule.id,
                        "rule_name": rule.name,
                        "alias": rule.alias,
                        "dining_start_time_cron_str": rule.dining_start_time_cron_str,
                        "dining_end_time_cron_str": rule.dining_end_time_cron_str,
                        "verify_start_time_cron_str": rule.verify_start_time_cron_str,
                        "verify_end_time_cron_str": rule.verify_end_time_cron_str,
                        "order_deadline": rule.order_deadline,
                        "cancellation_deadline": rule.cancellation_deadline,
                        "is_auto_verify": rule.is_auto_verify,
                        "status": rule.status,
                        "created_at": rule.created_at,
                        "updated_at": rule.updated_at
                    })
            else:
                return {
                    "total": 0,
                    "products": []
                }

            # 将产品信息添加到结果列表
            result_products.append({
                "product_id": product.id,
                "product_name": product.name,
                "product_price": product.price,
                "product_description": product.description,
                "product_status": product.status,
                "dining_rules": product_rules
            })
                
        return {
            "total": len(result_products),
            "products": result_products
        }

    def get_product_with_dining_rules_by_id(self, session: Session, product_id: int) -> Dict:
        """
        根据产品ID获取产品及其关联的dining_reservation类型规则
        
        Args:
            session: 数据库会话
            product_id: 产品ID
            
        Returns:
            Dict: 包含匹配的产品列表和总数的字典
        """
        # 查询指定ID的产品
        product = session.query(self.model).filter(
            self.model.id == product_id
        ).first()
        
        if not product:
            return {"total": 0, "products": []}
            
        result_products = []
        
        # 查询该产品关联的所有规则
        product_rules = []
        
        # 获取所有规则ID
        rule_ids = [rule.id for rule in product.rules if rule.type == RuleType.DINING_RESERVATION]
        
        # 一次性查询所有餐厅预订规则
        if rule_ids:
            dining_rule_objs = session.query(DiningReservationRule).filter(
                DiningReservationRule.id.in_(rule_ids)
            ).all()
            
            # 将规则转换为字典格式
            for rule in dining_rule_objs:
                product_rules.append({
                    "rule_id": rule.id,
                    "rule_name": rule.name,
                    "alias": rule.alias,
                    "dining_start_time_cron_str": rule.dining_start_time_cron_str,
                    "dining_end_time_cron_str": rule.dining_end_time_cron_str,
                    "verify_start_time_cron_str": rule.verify_start_time_cron_str,
                    "verify_end_time_cron_str": rule.verify_end_time_cron_str,
                    "order_deadline": rule.order_deadline,
                    "cancellation_deadline": rule.cancellation_deadline,
                    "is_auto_verify": rule.is_auto_verify,
                    "status": rule.status,
                    "created_at": rule.created_at,
                    "updated_at": rule.updated_at
                })
        
        # 将产品信息添加到结果列表
        result_products.append({
            "product_id": product.id,
            "product_name": product.name,
            "product_price": product.price,
            "product_description": product.description,
            "product_status": product.status,
            "dining_rules": product_rules
        })
            
        return {
            "total": len(result_products),
            "products": result_products
        }

    def get_by_category(self, session: Session, category_id: int, skip: int = 0, limit: int = 100) -> List[Product]:
        """根据分类ID获取产品列表

        Args:
            session: 数据库会话
            category_id: 分类ID
            skip: 跳过数量
            limit: 限制数量

        Returns:
            List[Product]: 产品列表
        """
        return session.query(self.model).filter(
            self.model.categories.any(ProductCategory.id == category_id),
            self.model.status == Status.ACTIVE
        ).offset(skip).limit(limit).all()

    def count(self, session: Session) -> int:
        """获取产品总数

        Args:
            session: 数据库会话

        Returns:
            int: 产品总数
        """
        return session.query(func.count(self.model.id)).scalar()

    def search_by_name(self, session: Session, name: str) -> List[Dict[str, int]]:
        """
        根据产品名称进行模糊搜索
        
        Args:
            session: 数据库会话
            name: 产品名称关键词
            
        Returns:
            List[Dict[str, int]]: 包含产品名称和ID的列表
        """
        search_name = f"%{name}%"
        products = session.query(self.model).filter(
            self.model.name.like(search_name),
            self.model.status == Status.ACTIVE
        ).all()
        
        return [{"name": product.name, "id": product.id} for product in products]

    def add_categories(self, session: Session, category_ids: List[int], product_id: int) -> dict:
        """批量将分类绑定到产品

        Args:
            session: 数据库会话
            category_ids: 分类ID列表
            product_id: 产品ID

        Returns:
            dict: 包含成功和失败的分类ID列表
        """
        product = session.query(Product).filter(Product.id == product_id).first()
        if not product:
            return {"success": [], "failed": category_ids}
        
        success_ids = []
        failed_ids = []

        for category_id in category_ids:
            # 获取ProductCategory实例
            category = session.query(ProductCategory).filter(ProductCategory.id == category_id).first()
            if not category:
                failed_ids.append(category_id)
                continue

            # 检查是否已经绑定
            if category not in product.categories:
                product.categories.append(category)
                success_ids.append(category_id)
            else:
                # 已经绑定过，也视为成功
                success_ids.append(category_id)

        session.commit()
        return {"success": success_ids, "failed": failed_ids}

    def remove_categories(self, session: Session, category_ids: List[int], product_id: int) -> dict:
        """批量将分类从产品解绑

        Args:
            session: 数据库会话
            category_ids: 分类ID列表
            product_id: 产品ID

        Returns:
            dict: 包含成功和失败的分类ID列表
        """
        product = session.query(Product).filter(Product.id == product_id).first()
        if not product:
            return {"success": [], "failed": category_ids}

        success_ids = []
        failed_ids = []

        for category_id in category_ids:
            # 获取ProductCategory实例
            category = session.query(ProductCategory).filter(ProductCategory.id == category_id).first()
            if not category:
                failed_ids.append(category_id)
                continue

            # 检查是否已经绑定
            if category in product.categories:
                # 移除关联关系
                product.categories.remove(category)
                success_ids.append(category_id)
            else:
                # 未绑定，也视为成功
                success_ids.append(category_id)

        session.commit()
        return {"success": success_ids, "failed": failed_ids}

    def update_categories(self, session: Session, category_ids: List[int], product_id: int) -> dict:
        """更新产品的分类绑定（先清空再重新绑定）

        Args:
            session: 数据库会话
            category_ids: 分类ID列表
            product_id: 产品ID

        Returns:
            dict: 包含成功和失败的分类ID列表
        """
        product = session.query(Product).filter(Product.id == product_id).first()
        if not product:
            return {"success": [], "failed": category_ids}
        
        # 先清空现有的分类绑定
        product.categories.clear()
        
        success_ids = []
        failed_ids = []

        if category_ids:  # 如果传入了分类ID
            for category_id in category_ids:
                # 获取ProductCategory实例
                category = session.query(ProductCategory).filter(ProductCategory.id == category_id).first()
                if not category:
                    failed_ids.append(category_id)
                    continue

                # 添加新的绑定
                product.categories.append(category)
                success_ids.append(category_id)

        session.commit()
        return {"success": success_ids, "failed": failed_ids}


class DirectSaleProductDAO(DAO):
    """直销产品 DAO 类"""

    def __init__(self):
        super().__init__(DirectSaleProduct)

    def create(self, session: Session, product: DirectSaleProductCreate) -> DirectSaleProduct:
        """创建直销产品"""
        product_data = product.model_dump()
        return super().create(session, **product_data)

    def get(self, session: Session, product_id: int) -> Optional[DirectSaleProduct]:
        """获取直销产品"""
        return super().get(session, product_id)

    def get_list(self, session: Session, skip: int = 0, limit: int = 100) -> List[DirectSaleProduct]:
        """获取直销产品列表"""
        return session.query(self.model).offset(skip).limit(limit).all()

    def update(self, session: Session, product_id: int, product: DirectSaleProductUpdate) -> Optional[
        DirectSaleProduct]:
        """更新直销产品"""
        product_data = product.model_dump(exclude_unset=True)
        if 'type' in product_data:
            # 不允许修改type
            del product_data['type']
        return super().update(session, product_id, **product_data)

    def delete(self, session: Session, product_id: int) -> bool:
        """删除直销产品"""
        return super().delete(session, product_id)


class ReservationProductDAO(DAO):
    """预订产品 DAO 类"""

    def __init__(self):
        super().__init__(ReservationProduct)

    def create(self, session: Session, product: ReservationProductCreate) -> ReservationProduct:
        """创建预订产品"""
        product_data = product.model_dump()
        return super().create(session, **product_data)

    def get(self, session: Session, product_id: int) -> Optional[ReservationProduct]:
        """获取预订产品"""
        return super().get(session, product_id)

    def get_list(self, session: Session, skip: int = 0, limit: int = 100) -> List[ReservationProduct]:
        """获取预订产品列表"""
        return session.query(self.model).offset(skip).limit(limit).all()

    def update(self, session: Session, product_id: int, product: ReservationProductUpdate) -> Optional[
        ReservationProduct]:
        """更新预订产品"""
        product_data = product.model_dump(exclude_unset=True)
        if 'type' in product_data:
            # 不允许修改type
            del product_data['type']
        return super().update(session, product_id, **product_data)

    def delete(self, session: Session, product_id: int) -> bool:
        """删除预订产品"""
        return super().delete(session, product_id)

    def get_available_reservations(self, session: Session, skip: int = 0, limit: int = 100) -> List[ReservationProduct]:
        """获取可预订的产品列表"""
        return session.query(self.model).filter(
            self.model.status == Status.ACTIVE,
            self.model.stock > 0
        ).offset(skip).limit(limit).all()


class ProductCategoryDAO(DAO):
    """产品分类 DAO 类"""

    def __init__(self):
        super().__init__(ProductCategory)

    def create(self, session: Session, category: ProductCategoryCreate) -> ProductCategory:
        """创建产品分类"""
        category_data = category.model_dump()
        # 如果 parent_id 为 0，则设置为 None（表示顶级分类）
        if category_data.get('parent_id') == 0:
            category_data['parent_id'] = None
        return super().create(session, **category_data)

    def get(self, session: Session, category_id: int) -> Optional[ProductCategory]:
        """获取产品分类"""
        return super().get(session, category_id)

    def get_list(self, session: Session, skip: int = 0, limit: int = 100) -> List[ProductCategory]:
        """获取产品分类列表"""
        return session.query(self.model).order_by(self.model.sort_order).offset(skip).limit(limit).all()

    def get_active_categories(self, session: Session, skip: int = 0, limit: int = 100) -> List[ProductCategory]:
        """获取活跃产品分类列表"""
        return session.query(self.model).filter(
            self.model.status == Status.ACTIVE
        ).order_by(self.model.sort_order).offset(skip).limit(limit).all()

    def get_parent_categories(self, session: Session, skip: int = 0, limit: int = 100) -> List[ProductCategory]:
        """获取所有父分类"""
        return session.query(self.model).filter(
            self.model.parent_id == None,
            self.model.status == Status.ACTIVE
        ).order_by(self.model.sort_order).offset(skip).limit(limit).all()

    def get_children_categories(self, session: Session, parent_id: int, skip: int = 0, limit: int = 100) -> List[ProductCategory]:
        """获取指定父分类的子分类"""
        return session.query(self.model).filter(
            self.model.parent_id == parent_id,
            self.model.status == Status.ACTIVE
        ).order_by(self.model.sort_order).offset(skip).limit(limit).all()

    def search(self, session: Session, keyword: str = None, name: str = None, 
               status: Status = None, parent_id: int = None, skip: int = 0, limit: int = 100, key: str = None) -> dict:
        """
        根据条件搜索产品分类列表及总数
        
        Args:
            session: 数据库会话
            keyword: 搜索关键字，可搜索分类名称、描述等
            name: 分类名称（模糊匹配）
            status: 分类状态
            parent_id: 父分类ID
            skip: 分页起始位置
            limit: 分页大小
            
        Returns:
            dict: 包含 'total'（搜索结果总数）和 'list'（分页后的分类列表）的字典
        """
        query = session.query(self.model)

        # 如果提供了关键字，则添加过滤条件
        if keyword:
            search_keyword = f"%{keyword}%"
            query = query.filter(
                or_(
                    self.model.name.like(search_keyword),
                    self.model.description.like(search_keyword)
                )
            )

        # 按分类名称过滤
        if name:
            search_name = f"%{name}%"
            query = query.filter(self.model.name.like(search_name))

        # 按状态过滤
        if status is not None:
            query = query.filter(self.model.status == status)

        # 按父分类过滤
        if parent_id is not None:
            query = query.filter(self.model.parent_id == parent_id)

        if key:
            query = query.filter(self.model.key == key)

        # 查询总数
        total = query.count()
        # 查询分页后的分类列表，按排序顺序排列
        items = query.order_by(self.model.sort_order, self.model.id.desc()).offset(skip).limit(limit).all()

        return {
            "total": total,
            "list": items
        }

    def update(self, session: Session, category_id: int, category: ProductCategoryUpdate) -> Optional[ProductCategory]:
        """更新产品分类"""
        category_data = category.model_dump(exclude_unset=True)
        # 如果 parent_id 为 0，则设置为 None（表示顶级分类）
        if category_data.get('parent_id') == 0:
            category_data['parent_id'] = None
        return super().update(session, category_id, **category_data)

    def delete(self, session: Session, category_id: int) -> bool:
        """删除产品分类"""
        # 检查是否有子分类
        children = self.get_children_categories(session, category_id)
        if children:
            return False
        
        # 检查是否有关联的产品
        category = self.get(session, category_id)
        if category and category.products:
            return False
            
        return super().delete(session, category_id)

    def count(self, session: Session) -> int:
        """获取产品分类总数"""
        return session.query(func.count(self.model.id)).scalar()

    def get_all_parent_categories(self, session: Session, skip: int = 0, limit: int = 100) -> List[ProductCategory]:
        """获取所有父分类"""
        return session.query(self.model).filter(
            self.model.parent_id == None
        ).order_by(self.model.sort_order).offset(skip).limit(limit).all()

    def get_all_children_categories(self, session: Session, parent_id: int, skip: int = 0, limit: int = 100) -> List[ProductCategory]:
        """获取指定父分类的子分类"""
        return session.query(self.model).filter(
            self.model.parent_id == parent_id
        ).order_by(self.model.sort_order).offset(skip).limit(limit).all()

    def get_tree(self, session: Session) -> List[dict]:
        """获取分类树形结构（递归所有层级）"""
        # 获取所有分类
        all_categories = session.query(self.model).order_by(self.model.sort_order).all()
        
        # 将分类按parent_id分组
        categories_by_parent = {}
        for category in all_categories:
            parent_id = category.parent_id
            if parent_id not in categories_by_parent:
                categories_by_parent[parent_id] = []
            categories_by_parent[parent_id].append(category)
        
        def build_tree(parent_id=None):
            """递归构建树形结构"""
            children = categories_by_parent.get(parent_id, [])
            result = []
            
            for category in children:
                category_dict = {
                    "id": category.id,
                    "name": category.name,
                    "description": category.description,
                    "image": category.image,
                    "sort_order": category.sort_order,
                    "status": category.status,
                    "parent_id": category.parent_id,
                    "created_at": category.created_at,
                    "updated_at": category.updated_at,
                    "key": category.key,
                    "children": build_tree(category.id)  # 递归获取子分类
                }
                result.append(category_dict)
            
            return result
        
        # 从顶级分类开始构建树
        return build_tree(None)

    def get_categories_by_key(self, session: Session, key: str, show_root: bool = True) -> List[ProductCategory]:
        """
        根据key获取分类及其所有子分类
        
        Args:
            session: 数据库会话
            key: 分类的key字段
            show_root: 是否包含key所在的根分类节点
            
        Returns:
            List[ProductCategory]: 分类列表
        """
        # 首先查找key对应的分类
        root_category = session.query(self.model).filter(
            self.model.key == key,
            self.model.status == Status.ACTIVE
        ).first()
        
        if not root_category:
            return []
        
        # 递归获取所有子分类
        def get_all_children(parent_id: int) -> List[ProductCategory]:
            """递归获取所有子分类"""
            children = session.query(self.model).filter(
                self.model.parent_id == parent_id,
                self.model.status == Status.ACTIVE
            ).order_by(self.model.sort_order).all()
            
            result = []
            for child in children:
                result.append(child)
                # 递归获取子分类的子分类
                result.extend(get_all_children(child.id))
            
            return result
        
        # 获取所有子分类
        all_children = get_all_children(root_category.id)
        
        # 根据show_root参数决定是否包含根节点
        if show_root:
            return [root_category] + all_children
        else:
            return all_children


# 创建 DAO 实例
product_dao = ProductDAO()
direct_sale_product_dao = DirectSaleProductDAO()
reservation_product_dao = ReservationProductDAO()
product_category_dao = ProductCategoryDAO()
